package com.hp.snapfish.ecommerce.model

import com.hp.snapfish.ecommerce.result.{CommerceErrorDetail, CommerceErrorJsonProtocol}
import com.hp.snapfish.ecommerce.scalalogging.Splunkable
import spray.json
import spray.json._

import java.text.DateFormat
import java.util.Date

 case class MetaData(name: String, value: String, metadataType: Option[String]) extends Splunkable

 case class DiscountDetail(
                            promotionName: String,
                            promotionId: Long,
                            accountPromotionId: Option[Long],
                            discountQty: Int = 0,
                            discountAmount: BigDecimal,
                            credit: Boolean = false,
                            isSalePrice: Boolean = false,
                            isDelayedGrant: Boolean = false,
                            employeeOnly: Boolean = false,
                            chargeShippingFee : Boolean = true,
                            delayedGrantPromo: Option[DelayedGrantPromotion] = None,
                            houseHoldFraudCheck: Boolean = false,
                            seqNo: Option[Int] = None,
                            prepaidPromotion: Option[Boolean] = Some(false),
                            couponNumber:Option[String] =None,
                            prepaidDiscountInfo: Option[PrepaidDiscountInfo] = None,
                            childPromotionName: Option[String] = None,
                            priority: Option[Int] = Some(1)) extends Splunkable

case class PrepaidDiscountInfo(
                                prepaidAmount: Option[BigDecimal] = Some(0),
                                prepaidTax: Option[Boolean] = None,
                                prepaidTaxCharged: Option[BigDecimal] = Some(0),
                                effectivePriceTier: Option[Int] = None
                              ) extends Splunkable

 case class DiscountEntry(
  totalDiscount: BigDecimal,
  details: List[DiscountDetail]) extends Splunkable

//helper for discounting
 case class DiscountEntryHelper(
  totalDiscount: BigDecimal,
  details: Map[Long, DiscountDetail])

 case class PromotionEntry(
  promotionId: Option[Long],
  accountPromotionId: Option[Long],
  promotionName: String,
  redemptionMethod: Option[RedemptionMethod],
  errors: List[CommerceErrorDetail]) extends Splunkable

 case class TaxDetailsJson(json: String)

trait TaxDetails

 case class TaxData(
  taxRate: BigDecimal,
  taxAmount: BigDecimal,
  taxDescription: Option[String]) extends Splunkable

 case class NationalAndStateTax(
  national: Option[TaxData],
  duty: Option[TaxData],
  state: Option[TaxData]) extends Splunkable

 case class TaxInfo(
  taxed: Boolean,
  source: String,
  countryCode: Option[String],
  stateCode: Option[String],
  rate: BigDecimal,
  shippingRate: BigDecimal,
  inclusive: Boolean,
  shipping: Boolean,
  productTax: Option[NationalAndStateTax],
  shippingTax: Option[NationalAndStateTax],
  //for Walgreens
  productDetails: List[TaxDetailsJson],
  shippingDetails: List[TaxDetailsJson]) extends Splunkable

 case class Error(
                         commerceErrorCode: String,
                         severity: String,
                         shipBinId: Long,
                         description: String) extends Splunkable

 case class LineItemOption(
  lineItemOptionId: Long,
  optionId: String,
  quantity: Int,
  units: Option[Int],
  sourceGroup: Option[String],
  productTotal: BigDecimal,
  productTax: BigDecimal,
  productDiscount: DiscountEntry,
  shippingTotal: BigDecimal,
  shippingTax: BigDecimal,
  shippingDiscount: DiscountEntry,
  shippingBundleId : Option[String] = None,
  taxInfo: TaxInfo,
  saveForLater : Option[Boolean] = Some(false),
  logicalProductTypeId: Option[String] = None,
  designerCode: Option[String] = None,
  metadata: Option[List[MetaData]],
  fairMarketValue: Option[Double] = None
  ) extends Splunkable

 case class LineItem(
  skuId: String,
  quantity: Int,
  lineItemId: Long,
  priceLevel: Int,
  units: Option[Int],
  sourceGroup: Option[String],
  vendor: Option[String],
  productTotal: BigDecimal,
  productTax: BigDecimal,
  productDiscount: DiscountEntry,
  shippingTotal: BigDecimal,
  shippingTax: BigDecimal,
  shippingDiscount: DiscountEntry,
  shippingBundleId : Option[String] = None,
  lineItemOptions: List[LineItemOption],
  spvId: Option[String] = None,
  logicalProductTypeId: Option[String] = None,
  designerCode: Option[String] = None,
  taxInfo: TaxInfo,
  metadata: Option[List[MetaData]],
  saveForLater : Option[Boolean] = Some(false),
  fairMarketValue: Option[Double] = None
  ) extends Splunkable

 case class ShipBin(
  shipBinId: Long,
  status: Option[String] = None,
  shippingAddressId: Option[String],
  vendorId: Option[String],
  deliveryMethod: DeliveryMethod,
  deliveryOption: Option[DeliveryOption],
  retailer : Option[String],
  storeId : Option[String],
  productTotal: BigDecimal,
  productTax: BigDecimal,
  productDiscount: DiscountEntry,
  shippingTotal: BigDecimal,
  shippingTax: BigDecimal,
  shippingDiscount: DiscountEntry,
  lineItems: List[LineItem],
  taxInfo: List[TaxInfo] = Nil,
  metadata: Option[List[MetaData]]) extends Splunkable

 case class Order(
  _id: Long,
  accountId: String,
  dataCenter : Option[String],
  appId: Option[String] = None,
  context: String,
  label: Option[String] = None,
  status: Option[String] = None,
  promotionCodes: List[PromotionEntry],
  promotionsToIgnore: List[PromotionEntry],
  shipBins: List[ShipBin],
  productTotal: BigDecimal,
  productTax: BigDecimal,
  productDiscount: DiscountEntry,
  shippingTotal: BigDecimal,
  shippingTax: BigDecimal,
  shippingDiscount: DiscountEntry,
  customerSegments: Option[List[String]],
  errors: Option[List[Error]],
  selectedDutyOption: Option[String],
  dutyOptions : Option[List[String]],
  metadata: Option[List[MetaData]]) extends Splunkable

object OrderData {
  case class PriceGroupData(context: String, deliveryMethod: DeliveryMethod, retailer: Option[String], sourceGroups: Set[Option[String]])
  case class OrderData(accountId: String, priceGroups: List[(Long, PriceGroupData)], vendors: Set[Option[String]], shippingAddresses: Set[Option[String]])

  def collectPriceGroupData(order: Order) = {
    var vendors = Set.empty[Option[String]]
    var shippingAddresses = Set.empty[Option[String]]
    val results =
      order.shipBins.map(shipBin => {
        var sourceGroups = Set.empty[Option[String]]
        vendors = vendors + shipBin.vendorId
        shippingAddresses = shippingAddresses + shipBin.shippingAddressId
        shipBin.lineItems match {
          case x :: xs =>
            shipBin.lineItems.map(lineItem => {
              sourceGroups = sourceGroups + lineItem.sourceGroup
              lineItem.lineItemOptions.map(lineItemOption => {
                sourceGroups = sourceGroups + lineItemOption.sourceGroup
              })
            })
          case Nil =>
            sourceGroups = sourceGroups + None
        }
        (shipBin.shipBinId, PriceGroupData(order.context, shipBin.deliveryMethod, shipBin.retailer, sourceGroups))
      })
    OrderData(accountId = order.accountId, priceGroups = results, vendors = vendors, shippingAddresses = shippingAddresses)
  }

  def shipBinData(orderData: OrderData) = {
    orderData.priceGroups.flatMap(e =>
      e._2.sourceGroups.toList.map(sg => (e._1, sg))
    )
  }
}

case class PricingRequest(
  buy: Boolean,
  order: Order,
  account: Option[Account],
  operation: PriceOperation.Value = PriceOperation.All)


sealed case class VertexInvoiceCallRequest(
                                     partner: String,
                                     accountId: String,
                                     context: String,
                                     orderId: Long,
                                     shipBin: ShipBin)

sealed case class VertexInvoiceCallResponse(
                                              response: String
                                            )

sealed case class DiscountVoidRequest(
                                       dataCenter: String,
                                       accountId: String,
                                       orderDiscountDetails: List[DiscountDetail])

sealed case class DiscountRefundRequest(
                                       accountId: String,
                                       dataCenter: String,
                                       discountRefundDetails: List[DiscountRefundVoidDetails])

sealed case class DiscountRefundVoidDetails(
                                           promotionId: Long,
                                           quantity: Int,
                                           seqNo:Option[Int]=None,
                                           couponNumber:Option[String]=None
                                           )

sealed case class DiscountRefundVoidResponse(
                                            details: List[DiscountRefundVoidDetails]
                                            )

sealed case class CouponWalletResponse(
  order: Order,
  promotions: List[String],
  promotionGroups: List[ApplicablePromotionGroup])

object TaxInfo {
  val ESTIMATED_TAX = "EstimatedTax"
  val NONE = "NotTaxed"
  val EXEMPT = "Exempt"
  val US = "US"
  val CA = "CA"
  val VERTEX = "Vertex"

  val DEFAULT =
    TaxInfo(
      taxed = true,
      source = ESTIMATED_TAX,
      countryCode = Some(US),
      stateCode = Some(CA),
      inclusive = false,
      shipping = true,
      rate = 0,
      shippingRate = 0,
      productTax = None,
      shippingTax = None,
      productDetails = Nil,
      shippingDetails = Nil
    )

  val NOT_TAXED =
    TaxInfo(
      taxed = false,
      source = NONE,
      countryCode = None,
      stateCode = None,
      inclusive = false,
      shipping = true,
      rate = 0,
      shippingRate = 0,
      productTax = None,
      shippingTax = None,
      productDetails = Nil,
      shippingDetails = Nil
    )

  def hasNationalTax(countryCode: String) = {
    countryCode match {
      case "US" => false
      case "UNKNOWN" => true
      case _ => true
    }
  }

  def hasStateTax(countryCode: String) = {
    countryCode match {
      case "US" => true
      case "CA" => true
      case "UNKNOWN" => true
      case _ => false
    }
  }


  def taxDescription(countryCode: String) = {
    countryCode match {
      case "US" => Some("SalesTax")
      case "AU" => Some("GST")
      case "NZ" => Some("GST")
      case "CA" => Some("GST/HST")
      case "UNKNOWN" => Some("UNKNOWN")
      case _ => Some("VAT")
    }
  }

  def stateTaxDescription(countryCode: String) = {
    countryCode match {
      case "US" => Some("SalesTax")
      case "AU" => Some("GST")
      case "NZ" => Some("GST")
      case "CA" => Some("PST/QST")
      case "UNKNOWN" => Some("UNKNOWN")
      case _ => Some("VAT")
    }
  }

  def nationalTax(countryCode: String, taxRate: BigDecimal, taxAmount: BigDecimal, taxShipping: Boolean) = {
    if (TaxInfo.hasNationalTax(countryCode))
      if (taxShipping)
        Some(TaxData(taxRate, taxAmount, taxDescription(countryCode)))
      else
        Some(TaxData(0, 0, taxDescription(countryCode)))
    else
      None
  }

  def stateTax(countryCode: String, taxRate: BigDecimal, taxAmount: BigDecimal, taxShipping: Boolean) = {
    if (TaxInfo.hasStateTax(countryCode))
      if (taxShipping)
        Some(TaxData(taxRate, taxAmount, stateTaxDescription(countryCode)))
      else
        Some(TaxData(0, 0, stateTaxDescription(countryCode)))
    else
      None
  }
}

trait OrderJsonProtocol extends DefaultJsonProtocol with PromotionJsonProtocol with CommerceErrorJsonProtocol with AccountJsonProtocol {

  implicit object PriceOperationJsonFormat extends RootJsonFormat[PriceOperation.Value] {
    def write(e: PriceOperation.Value) = JsString(e.toString)

    def read(v: JsValue) = v match {
      case JsString(s) => PriceOperation.withName(s)
      case _ => json.deserializationError("String expected")
    }
  }

  implicit object DateJsonFormat extends RootJsonFormat[java.util.Date] {
    def write(d: Date) = JsString(d.toString)

    def read(v: JsValue) = v match {
      case JsString(date) => {
        val dateFormat = DateFormat.getDateInstance
        dateFormat.parse(date)
      }
      case _ => json.deserializationError("Date expected")
    }
  }

  implicit object TaxDetailsJsonFormat extends RootJsonFormat[TaxDetailsJson] {
    def write(d: TaxDetailsJson) = JsonParser.apply(d.json)
    def read(v: JsValue): TaxDetailsJson = TaxDetailsJson(v.toString)
  }

//  implicit object TaxInfoJsonFormat extends RootJsonFormat[TaxInfo] {
//    def write(d: TaxInfo) =
//      JsObject(
//        "taxed" -> BooleanJsonFormat.write(d.taxed),
//        "source" -> StringJsonFormat.write(d.source),
//        "countryCode" -> optionFormat[String].write(d.countryCode),
//        "stateCode" -> optionFormat[String].write(d.stateCode),
//        "inclusive" -> BooleanJsonFormat.write(d.inclusive),
//        "shipping" -> BooleanJsonFormat.write(d.shipping),
//        "rate" -> BigDecimalJsonFormat.write(d.rate),
//        "shippingRate" -> BigDecimalJsonFormat.write(d.shippingRate),
//        "productTax" -> nationalAndStateTaxFormat.write(d.productTax),
//        "shippingTax" -> nationalAndStateTaxFormat.write(d.shippingTax),
//        "productDetails" -> listFormat[TaxDetailsJson].write(d.productDetails),
//        "shippingDetails" -> listFormat[TaxDetailsJson].write(d.shippingDetails)
//      )
//    def read(v: JsValue): TaxInfo = TaxInfo.NOT_TAXED
//  }

  implicit val metadataFormat = jsonFormat3(MetaData)
  implicit val promotionCodeFormat = jsonFormat5(PromotionEntry)
  implicit val prepaidDiscountInfoFormat = jsonFormat4(PrepaidDiscountInfo)
  implicit val delayedGrantPromoFormat = jsonFormat3(DelayedGrantPromotion)
  implicit val discountDetailFormat = jsonFormat18(DiscountDetail)
  implicit val discountEntryFormat = jsonFormat2(DiscountEntry)
  implicit val taxDataFormat = jsonFormat3(TaxData)
  implicit val nationalAndStateTaxFormat = jsonFormat3(NationalAndStateTax)
  implicit val taxInfoFormat = jsonFormat(TaxInfo.apply,
    "taxed",
    "source",
    "countryCode",
    "stateCode",
    "rate",
    "shippingRate",
    "inclusive",
    "shipping",
    "productTax",
    "shippingTax",
    "productDetails",
    "shippingDetails")
  implicit val errorFormat = jsonFormat4(Error)
  implicit val lineItemOptionFormat = jsonFormat18(LineItemOption)
  implicit val lineItemPriceFormat = jsonFormat22(LineItem)
  implicit val shipBinPriceFormat = jsonFormat17(ShipBin)

  implicit val orderPriceFormat = jsonFormat21(Order)
  implicit val pricingRequestFormat = jsonFormat4(PricingRequest)
  implicit val VertexInvoiceCallRequestFormat = jsonFormat5(VertexInvoiceCallRequest)
  implicit val walletResponseFormat = jsonFormat3(CouponWalletResponse)
  implicit val discountRefundVoidDetails = jsonFormat4(DiscountRefundVoidDetails)
  implicit val discountRefundRequestFormat = jsonFormat3(DiscountRefundRequest)
  implicit val discountVoidRequestFormat = jsonFormat3(DiscountVoidRequest)
  implicit val discountRefundVoidResponse = jsonFormat1(DiscountRefundVoidResponse)
  implicit val vertexInvoiceCallResponse = jsonFormat1(VertexInvoiceCallResponse)
}
